"use client";

import { usePathname } from "next/navigation";
import { ThemeToggle } from "@/components/theme-toggle";
import { BackButton } from "@/components/back-button";
import { BreadcrumbNav } from "@/components/breadcrumb-nav";

export function Header() {
  return (
    <header className="border-b">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <img src="ldis.png" alt="LDIS" className="h-7 w-7" />
            <h1 className="text-xl font-bold">LDIS</h1>
          </div>
          <div className="flex items-center space-x-2">
            <ThemeToggle />
          </div>
        </div>
      </div>
      <HeaderNavigation />
    </header>
  );
}

function HeaderNavigation() {
  const pathname = usePathname();

  // Don't show navigation on home page
  if (pathname === "/") {
    return null;
  }

  return (
    <div className="px-4 pb-3">
      <div className="flex items-center gap-4">
        <BackButton />
        <BreadcrumbNav />
      </div>
    </div>
  );
}
