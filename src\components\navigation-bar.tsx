import { BackButton } from "@/components/back-button";
import { BreadcrumbNav } from "@/components/breadcrumb-nav";

interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface NavigationBarProps {
  showBackButton?: boolean;
  backButtonHref?: string;
  backButtonText?: string;
  breadcrumbItems?: BreadcrumbItem[];
  className?: string;
}

export function NavigationBar({
  showBackButton = true,
  backButtonHref,
  backButtonText,
  breadcrumbItems,
  className = "",
}: NavigationBarProps) {
  return (
    <div className={`flex items-center justify-between py-4 px-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 ${className}`}>
      <div className="flex items-center gap-4">
        {showBackButton && (
          <BackButton href={backButtonHref}>
            {backButtonText}
          </BackButton>
        )}
        <BreadcrumbNav items={breadcrumbItems} />
      </div>
    </div>
  );
}
