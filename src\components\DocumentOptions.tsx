import Link from "next/link";
import { FileText } from "@deemlol/next-icons";
import { ArrowRight } from "@deemlol/next-icons";

export function DocumentOptions() {
  const boxes = [
    {
      label: "Good Moral",
      href: "/pages/good_moral",
      icon_File: FileText,
      icon_ArrowRight: ArrowRight,
    },
    {
      label: "Affidavit",
      href: "/",
      icon_File: FileText,
      icon_ArrowRight: ArrowRight,
    },
    {
      label: "clearance",
      href: "/",
      icon_File: FileText,
      icon_ArrowRight: ArrowRight,
    },
    {
      label: "License",
      href: "/",
      icon_File: FileText,
      icon_ArrowRight: ArrowRight,
    },
  ];

  return (
    <div className="flex flex-col gap-4 justify-center items-center min-h-[calc(100vh-80px)]">
      <h1 className="font-medium">Apply for?</h1>
      <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {boxes.map((box, index) => (
          <Link
            key={index}
            href={box.href}
            className="h-25 w-25 bg-card shadow-md rounded-sm flex flex-col items-center justify-center gap-2 p-2"
          >
            <box.icon_File className="h-8 w-8 text-chart-3" />
            <h1 className="font-medium text-sm">{box.label}</h1>
          </Link>
        ))}
      </div>
    </div>
  );
}
