"use client";

import { usePathname } from "next/navigation";
import { BackButton } from "@/components/back-button";
import { BreadcrumbNav } from "@/components/breadcrumb-nav";

export function PageNavigation() {
  const pathname = usePathname();

  // Don't show navigation on home page
  if (pathname === "/") {
    return null;
  }

  return (
    <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 py-3">
        <div className="flex items-center gap-4">
          <BackButton />
          <BreadcrumbNav />
        </div>
      </div>
    </div>
  );
}
